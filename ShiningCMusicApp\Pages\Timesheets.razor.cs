using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using ShiningCMusicApp.Services;
using ShiningCMusicApp.Services.Interfaces;
using ShiningCMusicApp.Components;
using ShiningCMusicCommon.Models;
using ShiningCMusicCommon.Enums;
using Syncfusion.XlsIO;
using Syncfusion.Drawing;

namespace ShiningCMusicApp.Pages
{
    public partial class TimesheetsBase : CommonPageBase
    {
        [Inject] protected ITimesheetApiService TimesheetApi { get; set; } = default!;
        [Inject] protected IStudentApiService StudentApi { get; set; } = default!;
        [Inject] protected ITutorApiService TutorApi { get; set; } = default!;
        [Inject] protected ISubjectApiService SubjectApi { get; set; } = default!;
        [Inject] protected CustomAuthenticationStateProvider AuthStateProvider { get; set; } = default!;
        [Inject] protected IJSRuntime JSRuntime { get; set; } = default!;
        [Inject] protected NavigationManager Navigation { get; set; } = default!;
        [Inject] protected IDialogService DialogService { get; set; } = default!;

        // Data collections
        protected List<Timesheet> timesheets = new();
        protected List<Student> students = new();
        protected List<Tutor> tutors = new();
        protected List<Subject> subjects = new();
        protected List<TimesheetEntry> timesheetEntries = new();

        // Modal state
        protected bool showModal = false;
        protected bool showViewModal = false;
        protected bool showEntryModal = false;
        protected bool showBulkEntryModal = false;
        protected bool isEditMode = false;
        protected bool isEditEntryMode = false;
        protected bool isLoading = true;
        protected bool isSaving = false;
        protected bool isSavingEntry = false;
        protected string modalTitle = "";
        protected string entryModalTitle = "";

        // Current items
        protected Timesheet currentTimesheet = new();
        protected Timesheet? selectedTimesheet = null;
        protected TimesheetEntry currentEntry = new();

        // Bulk entry creation
        protected int numberOfRecords = 5;

        // Role-based permissions
        protected AuthenticationState? authState;
        protected User? currentUser;
        protected bool IsAdmin => authState?.User?.IsInRole(UserRoleEnum.Administrator.ToString()) == true;
        protected bool IsTutor => authState?.User?.IsInRole(UserRoleEnum.Tutor.ToString()) == true;
        protected bool CanCreateTimesheets => IsAdmin;
        protected bool CanEditTimesheets => IsAdmin;

        // Helper methods
        protected string GetStudentName(int? studentId)
        {
            if (studentId == null) return "Unknown Student";
            return students.FirstOrDefault(s => s.StudentId == studentId)?.StudentName ?? "Unknown Student";
        }

        protected string GetTutorName(int? tutorId)
        {
            if (tutorId == null) return "Unknown Tutor";
            return tutors.FirstOrDefault(t => t.TutorId == tutorId)?.TutorName ?? "Unknown Tutor";
        }

        protected override async Task OnInitializedAsync()
        {
            // Get authentication state first
            authState = await AuthStateProvider.GetAuthenticationStateAsync();
            currentUser = await AuthStateProvider.GetCurrentUserAsync();
            await LoadData();
        }

        protected async Task LoadData()
        {
            isLoading = true;
            try
            {
                // Load reference data
                var studentsTask = StudentApi.GetStudentsAsync();
                var tutorsTask = TutorApi.GetTutorsAsync();
                var subjectsTask = SubjectApi.GetSubjectsAsync();

                await Task.WhenAll(studentsTask, tutorsTask, subjectsTask);

                students = await studentsTask;
                tutors = await tutorsTask;
                subjects = await subjectsTask;

                // Load timesheets based on role
                if (IsAdmin)
                {
                    timesheets = await TimesheetApi.GetTimesheetsAsync();
                }
                else if (IsTutor && currentUser != null)
                {
                    // Find tutor ID for current user
                    var tutor = tutors.FirstOrDefault(t => t.LoginName == currentUser.LoginName);
                    if (tutor != null)
                    {
                        timesheets = await TimesheetApi.GetTimesheetsByTutorAsync(tutor.TutorId);
                    }
                }

                await JSRuntime.InvokeVoidAsync("console.log", $"Loaded {timesheets.Count} timesheets");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error loading data: {ex.Message}");
                await DialogService.ShowErrorAsync("Error loading data", ex.Message);
            }
            finally
            {
                isLoading = false;
            }
        }

        protected async Task RefreshData()
        {
            await LoadData();
        }

        protected void OpenCreateModal()
        {
            currentTimesheet = new Timesheet 
            { 
                StartDate = DateTime.Today,
                ClassDurationMinutes = 30
            };
            isEditMode = false;
            modalTitle = "Create New Timesheet";
            showModal = true;
        }

        protected void OpenEditModal(Timesheet? timesheet)
        {
            if (timesheet != null)
            {
                currentTimesheet = new Timesheet
                {
                    TimesheetId = timesheet.TimesheetId,
                    StudentId = timesheet.StudentId,
                    TutorId = timesheet.TutorId,
                    SubjectId = timesheet.SubjectId,
                    StartDate = timesheet.StartDate,
                    ContactNumber = timesheet.ContactNumber,
                    ClassDurationMinutes = timesheet.ClassDurationMinutes,
                    Notes = timesheet.Notes
                };

                isEditMode = true;
                modalTitle = "Edit Timesheet";
                showModal = true;
            }
        }

        protected async Task ViewTimesheet(Timesheet? timesheet)
        {
            if (timesheet != null)
            {
                selectedTimesheet = timesheet;
                timesheetEntries = await TimesheetApi.GetTimesheetEntriesAsync(timesheet.TimesheetId);
                showViewModal = true;
            }
        }

        protected void CloseModal()
        {
            showModal = false;
            currentTimesheet = new();
        }

        protected void CloseViewModal()
        {
            showViewModal = false;
            selectedTimesheet = null;
            timesheetEntries.Clear();
        }

        protected async Task SaveTimesheet()
        {
            isSaving = true;
            try
            {
                if (currentTimesheet.StudentId <= 0 || currentTimesheet.TutorId <= 0 ||
                    currentTimesheet.SubjectId <= 0 || currentTimesheet.ClassDurationMinutes <= 0)
                {
                    await DialogService.ShowErrorAsync("Validation Error", "Please fill in all required fields.");
                    return;
                }



                bool success;
                if (isEditMode)
                {
                    success = await TimesheetApi.UpdateTimesheetAsync(currentTimesheet.TimesheetId, currentTimesheet);
                }
                else
                {
                    var created = await TimesheetApi.CreateTimesheetAsync(currentTimesheet);
                    success = created != null;
                }

                if (success)
                {
                    await DialogService.ShowSuccessAsync("Success",
                        isEditMode ? "Timesheet updated successfully!" : "Timesheet created successfully!");
                    CloseModal();
                    await RefreshData();
                }
                else
                {
                    await DialogService.ShowErrorAsync("Error",
                        isEditMode ? "Failed to update timesheet." : "Failed to create timesheet.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error saving timesheet: {ex.Message}");
                await DialogService.ShowErrorAsync("Error", $"An error occurred: {ex.Message}");
            }
            finally
            {
                isSaving = false;
            }
        }

        protected async Task DeleteTimesheet(Timesheet? timesheet)
        {
            if (timesheet != null)
            {
                var studentName = students.FirstOrDefault(s => s.StudentId == timesheet.StudentId)?.StudentName ?? "Unknown Student";
                var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                    $"Are you sure you want to delete the timesheet for {studentName}?",
                    "This action cannot be undone.",
                    "Confirm Delete");

                if (confirmed)
                {
                    try
                    {
                        var success = await TimesheetApi.DeleteTimesheetAsync(timesheet.TimesheetId);
                        if (success)
                        {
                            await DialogService.ShowSuccessAsync("Success", "Timesheet deleted successfully!");
                            await RefreshData();
                        }
                        else
                        {
                            await DialogService.ShowErrorAsync("Error", "Failed to delete timesheet.");
                        }
                    }
                    catch (Exception ex)
                    {
                        await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting timesheet: {ex.Message}");
                        await DialogService.ShowErrorAsync("Error", $"An error occurred: {ex.Message}");
                    }
                }
            }
        }

        protected void OnStudentChanged(Syncfusion.Blazor.DropDowns.ChangeEventArgs<int, Student> args)
        {
            if (args.Value > 0)
            {
                var selectedStudent = students.FirstOrDefault(s => s.StudentId == args.Value);
                if (selectedStudent != null)
                {
                    // Auto-select tutor and subject if student has them assigned
                    if (selectedStudent.TutorID.HasValue)
                    {
                        currentTimesheet.TutorId = selectedStudent.TutorID.Value;
                    }
                    if (selectedStudent.SubjectId.HasValue)
                    {
                        currentTimesheet.SubjectId = selectedStudent.SubjectId.Value;
                    }

                    // Trigger UI update to reflect the cascaded values
                    StateHasChanged();
                }
            }
        }

        // TimesheetEntry methods
        protected void OpenAddEntryModal()
        {
            // Default to bulk create modal
            OpenBulkCreateModal();
        }

        protected void OpenBulkCreateModal()
        {
            numberOfRecords = 5; // Default to 5 records
            showBulkEntryModal = true;
        }

        protected void OpenSingleEntryModal()
        {
            currentEntry = new TimesheetEntry
            {
                TimesheetId = selectedTimesheet?.TimesheetId ?? 0,
                AttendanceDateTime = null, // Leave empty as per requirements
                IsPresent = false // Default to false as per requirements
            };
            isEditEntryMode = false;
            entryModalTitle = "Add Single Attendance Entry";
            showEntryModal = true;
        }

        protected void OpenEditEntryModal(TimesheetEntry? entry)
        {
            if (entry != null)
            {
                currentEntry = new TimesheetEntry
                {
                    TimesheetEntryId = entry.TimesheetEntryId,
                    TimesheetId = entry.TimesheetId,
                    RecordId = entry.RecordId,
                    AttendanceDateTime = entry.AttendanceDateTime,
                    Signature = entry.Signature,
                    IsPresent = entry.IsPresent,
                    Notes = entry.Notes
                };

                isEditEntryMode = true;
                entryModalTitle = "Edit Attendance Entry";
                showEntryModal = true;
            }
        }

        protected void CloseEntryModal()
        {
            showEntryModal = false;
            currentEntry = new();
        }

        protected void CloseBulkEntryModal()
        {
            showBulkEntryModal = false;
            numberOfRecords = 5;
        }

        protected async Task SaveTimesheetEntry()
        {
            isSavingEntry = true;
            try
            {
                if (currentEntry.TimesheetId <= 0)
                {
                    await DialogService.ShowErrorAsync("Error", "Invalid timesheet reference.");
                    return;
                }

                bool success;
                if (isEditEntryMode)
                {
                    success = await TimesheetApi.UpdateTimesheetEntryAsync(currentEntry.TimesheetEntryId, currentEntry);
                }
                else
                {
                    var created = await TimesheetApi.CreateTimesheetEntryAsync(currentEntry);
                    success = created != null;
                }

                if (success)
                {
                    await DialogService.ShowSuccessAsync("Success",
                        isEditEntryMode ? "Entry updated successfully!" : "Entry added successfully!");
                    CloseEntryModal();

                    // Refresh entries for current timesheet
                    if (selectedTimesheet != null)
                    {
                        timesheetEntries = await TimesheetApi.GetTimesheetEntriesAsync(selectedTimesheet.TimesheetId);
                    }
                }
                else
                {
                    await DialogService.ShowErrorAsync("Error",
                        isEditEntryMode ? "Failed to update entry." : "Failed to add entry.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error saving entry: {ex.Message}");
                await DialogService.ShowErrorAsync("Error", $"An error occurred: {ex.Message}");
            }
            finally
            {
                isSavingEntry = false;
            }
        }

        protected async Task CreateBulkTimesheetEntries()
        {
            isSavingEntry = true;
            try
            {
                if (selectedTimesheet == null)
                {
                    await DialogService.ShowErrorAsync("Error", "No timesheet selected.");
                    return;
                }

                if (numberOfRecords <= 0 || numberOfRecords > 50)
                {
                    await DialogService.ShowErrorAsync("Validation Error", "Number of records must be between 1 and 50.");
                    return;
                }

                var createdEntries = await TimesheetApi.CreateMultipleTimesheetEntriesAsync(selectedTimesheet.TimesheetId, numberOfRecords);

                if (createdEntries != null && createdEntries.Count > 0)
                {
                    await DialogService.ShowSuccessAsync("Success", $"{createdEntries.Count} attendance records created successfully!");
                    CloseBulkEntryModal();

                    // Refresh entries for current timesheet
                    timesheetEntries = await TimesheetApi.GetTimesheetEntriesAsync(selectedTimesheet.TimesheetId);
                }
                else
                {
                    await DialogService.ShowErrorAsync("Error", "Failed to create attendance records.");
                }
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error creating bulk entries: {ex.Message}");
                await DialogService.ShowErrorAsync("Error", $"An error occurred: {ex.Message}");
            }
            finally
            {
                isSavingEntry = false;
            }
        }

        protected async Task DeleteTimesheetEntry(TimesheetEntry? entry)
        {
            if (entry != null)
            {
                var confirmed = await DialogService.ShowDeleteConfirmationAsync(
                    "Are you sure you want to delete this attendance entry?",
                    "This action cannot be undone.",
                    "Confirm Delete");

                if (confirmed)
                {
                    try
                    {
                        var success = await TimesheetApi.DeleteTimesheetEntryAsync(entry.TimesheetEntryId);
                        if (success)
                        {
                            await DialogService.ShowSuccessAsync("Success", "Entry deleted successfully!");
                            
                            // Refresh entries for current timesheet
                            if (selectedTimesheet != null)
                            {
                                timesheetEntries = await TimesheetApi.GetTimesheetEntriesAsync(selectedTimesheet.TimesheetId);
                            }
                        }
                        else
                        {
                            await DialogService.ShowErrorAsync("Error", "Failed to delete entry.");
                        }
                    }
                    catch (Exception ex)
                    {
                        await JSRuntime.InvokeVoidAsync("console.error", $"Error deleting entry: {ex.Message}");
                        await DialogService.ShowErrorAsync("Error", $"An error occurred: {ex.Message}");
                    }
                }
            }
        }

        /// <summary>
        /// Exports the currently selected timesheet and its entries to an Excel spreadsheet.
        /// Creates a formatted Excel file with:
        /// - Timesheet header information (student, tutor, subject, dates, etc.)
        /// - All attendance records with date/time, presence status, signatures, and notes
        /// - Professional styling with headers and proper formatting
        /// - Automatic filename generation: Timesheet_{StudentName}_{Date}.xlsx
        /// </summary>
        protected async Task ExportTimesheetToExcel()
        {
            if (selectedTimesheet == null)
            {
                await DialogService.ShowErrorAsync("Error", "No timesheet selected for export.");
                return;
            }

            try
            {
                // Create Excel engine
                using var excelEngine = new ExcelEngine();
                var application = excelEngine.Excel;
                application.DefaultVersion = ExcelVersion.Xlsx;

                // Create workbook and worksheet
                var workbook = application.Workbooks.Create(1);
                var worksheet = workbook.Worksheets[0];
                worksheet.Name = "Timesheet";

                // Set up header styling
                var headerStyle = workbook.Styles.Add("HeaderStyle");
                headerStyle.Font.Bold = true;
                headerStyle.Font.Size = 12;
                headerStyle.Color = Color.FromArgb(79, 129, 189);
                headerStyle.Interior.Color = Color.FromArgb(184, 204, 228);

                var subHeaderStyle = workbook.Styles.Add("SubHeaderStyle");
                subHeaderStyle.Font.Bold = true;
                subHeaderStyle.Font.Size = 11;
                subHeaderStyle.Interior.Color = Color.FromArgb(220, 230, 241);

                // Title
                worksheet.Range["A1"].Text = "Timesheet Export";
                worksheet.Range["A1"].CellStyle = headerStyle;
                worksheet.Range["A1:H1"].Merge();

                // Timesheet Details Section
                var row = 3;
                worksheet.Range[$"A{row}"].Text = "Timesheet Details";
                worksheet.Range[$"A{row}"].CellStyle = subHeaderStyle;
                worksheet.Range[$"A{row}:H{row}"].Merge();

                row++;
                worksheet.Range[$"A{row}"].Text = "Student:";
                worksheet.Range[$"B{row}"].Text = GetStudentName(selectedTimesheet.StudentId);
                worksheet.Range[$"D{row}"].Text = "Tutor:";
                worksheet.Range[$"E{row}"].Text = GetTutorName(selectedTimesheet.TutorId);

                row++;
                worksheet.Range[$"A{row}"].Text = "Subject:";
                worksheet.Range[$"B{row}"].Text = subjects.FirstOrDefault(s => s.SubjectId == selectedTimesheet.SubjectId)?.SubjectName ?? "Unknown Subject";
                worksheet.Range[$"D{row}"].Text = "Start Date:";
                worksheet.Range[$"E{row}"].Text = selectedTimesheet.StartDate.ToString("dd/MM/yyyy");

                row++;
                worksheet.Range[$"A{row}"].Text = "Duration:";
                worksheet.Range[$"B{row}"].Text = $"{selectedTimesheet.ClassDurationMinutes} minutes";
                worksheet.Range[$"D{row}"].Text = "Contact:";
                worksheet.Range[$"E{row}"].Text = selectedTimesheet.ContactNumber ?? "";

                row++;
                worksheet.Range[$"A{row}"].Text = "Created:";
                worksheet.Range[$"B{row}"].Text = selectedTimesheet.CreatedUTC.ToString("dd/MM/yyyy");

                if (!string.IsNullOrEmpty(selectedTimesheet.Notes))
                {
                    row++;
                    worksheet.Range[$"A{row}"].Text = "Notes:";
                    worksheet.Range[$"B{row}:H{row}"].Text = selectedTimesheet.Notes;
                    worksheet.Range[$"B{row}:H{row}"].Merge();
                }

                // Attendance Records Section
                row += 2;
                worksheet.Range[$"A{row}"].Text = "Attendance Records";
                worksheet.Range[$"A{row}"].CellStyle = subHeaderStyle;
                worksheet.Range[$"A{row}:H{row}"].Merge();

                // Attendance Headers
                row++;
                worksheet.Range[$"A{row}"].Text = "Record #";
                worksheet.Range[$"B{row}"].Text = "Date & Time";
                worksheet.Range[$"C{row}"].Text = "Present";
                worksheet.Range[$"D{row}"].Text = "Signature";
                worksheet.Range[$"E{row}"].Text = "Notes";
                worksheet.Range[$"F{row}"].Text = "Created";

                // Apply header style to attendance headers
                worksheet.Range[$"A{row}:F{row}"].CellStyle = headerStyle;

                // Attendance Data
                foreach (var entry in timesheetEntries.OrderBy(e => e.RecordId ?? 999999).ThenBy(e => e.AttendanceDateTime))
                {
                    row++;
                    worksheet.Range[$"A{row}"].Text = entry.RecordId?.ToString() ?? "";
                    worksheet.Range[$"B{row}"].Text = entry.AttendanceDateTime?.ToString("dd/MM/yyyy HH:mm") ?? "";
                    worksheet.Range[$"C{row}"].Text = entry.IsPresent ? "Yes" : "No";
                    worksheet.Range[$"D{row}"].Text = entry.Signature ?? "";
                    worksheet.Range[$"E{row}"].Text = entry.Notes ?? "";
                    worksheet.Range[$"F{row}"].Text = entry.CreatedUTC.ToString("dd/MM/yyyy");
                }

                // Auto-fit columns
                worksheet.UsedRange.AutofitColumns();

                // Generate filename
                var studentName = GetStudentName(selectedTimesheet.StudentId).Replace(" ", "_");
                var timestamp = DateTime.Now.ToString("yyyy-MM-dd");
                var fileName = $"Timesheet_{studentName}_{timestamp}.xlsx";

                // Save and download
                var stream = new MemoryStream();
                workbook.SaveAs(stream);
                stream.Position = 0;

                // Convert to byte array for download
                var fileBytes = stream.ToArray();
                await JSRuntime.InvokeVoidAsync("downloadFile", fileName, Convert.ToBase64String(fileBytes));

                await DialogService.ShowSuccessAsync("Export Successful", $"Timesheet has been exported to {fileName}");
            }
            catch (Exception ex)
            {
                await JSRuntime.InvokeVoidAsync("console.error", $"Error exporting timesheet: {ex.Message}");
                await DialogService.ShowErrorAsync("Export Error", $"An error occurred while exporting: {ex.Message}");
            }
        }
    }
}
