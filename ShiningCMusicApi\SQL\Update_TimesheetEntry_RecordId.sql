USE [MusicSchool]
GO

-- Add RecordId column to TimesheetEntries table
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TimesheetEntries]') AND name = 'RecordId')
BEGIN
    ALTER TABLE [dbo].[TimesheetEntries]
    ADD [RecordId] [int] NULL
    
    PRINT 'RecordId column added to TimesheetEntries table'
END
ELSE
BEGIN
    PRINT 'RecordId column already exists in TimesheetEntries table'
END
GO

-- Make AttendanceDateTime nullable (if it's not already)
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[TimesheetEntries]') AND name = 'AttendanceDateTime' AND is_nullable = 0)
BEGIN
    -- First, we need to drop the index if it exists
    IF EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_TimesheetEntries_AttendanceDate' AND object_id = OBJECT_ID(N'[dbo].[TimesheetEntries]'))
    BEGIN
        DROP INDEX [IX_TimesheetEntries_AttendanceDate] ON [dbo].[TimesheetEntries]
        PRINT 'Dropped index IX_TimesheetEntries_AttendanceDate'
    END
    
    -- Alter the column to allow NULL
    ALTER TABLE [dbo].[TimesheetEntries]
    ALTER COLUMN [AttendanceDateTime] [datetime] NULL
    
    -- Recreate the index
    CREATE NONCLUSTERED INDEX [IX_TimesheetEntries_AttendanceDate] ON [dbo].[TimesheetEntries]
    (
        [AttendanceDateTime] ASC
    )
    WHERE [AttendanceDateTime] IS NOT NULL
    
    PRINT 'AttendanceDateTime column updated to allow NULL values'
    PRINT 'Index IX_TimesheetEntries_AttendanceDate recreated with WHERE clause'
END
ELSE
BEGIN
    PRINT 'AttendanceDateTime column is already nullable or does not exist'
END
GO

-- Update the default constraint for IsPresent to be false instead of true
IF EXISTS (SELECT * FROM sys.default_constraints WHERE name = 'DF_TimesheetEntries_IsPresent' AND parent_object_id = OBJECT_ID(N'[dbo].[TimesheetEntries]'))
BEGIN
    ALTER TABLE [dbo].[TimesheetEntries] DROP CONSTRAINT [DF_TimesheetEntries_IsPresent]
    ALTER TABLE [dbo].[TimesheetEntries] ADD CONSTRAINT [DF_TimesheetEntries_IsPresent] DEFAULT ((0)) FOR [IsPresent]
    PRINT 'Updated IsPresent default constraint to false (0)'
END
ELSE
BEGIN
    -- If constraint doesn't exist, create it
    ALTER TABLE [dbo].[TimesheetEntries] ADD CONSTRAINT [DF_TimesheetEntries_IsPresent] DEFAULT ((0)) FOR [IsPresent]
    PRINT 'Created IsPresent default constraint as false (0)'
END
GO

-- Create an index on RecordId for better performance when querying by record number
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_TimesheetEntries_RecordId' AND object_id = OBJECT_ID(N'[dbo].[TimesheetEntries]'))
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TimesheetEntries_RecordId] ON [dbo].[TimesheetEntries]
    (
        [RecordId] ASC
    )
    WHERE [RecordId] IS NOT NULL
    
    PRINT 'Index IX_TimesheetEntries_RecordId created'
END
ELSE
BEGIN
    PRINT 'Index IX_TimesheetEntries_RecordId already exists'
END
GO

PRINT 'TimesheetEntry table update completed successfully'
GO
