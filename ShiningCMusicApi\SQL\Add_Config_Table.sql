USE [MusicSchool]
GO

-- Create Config table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Config' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[Config](
        [ConfigId] [int] IDENTITY(1,1) NOT NULL,
        [GroupId] [int] NOT NULL,
        [Key] [nvarchar](100) NOT NULL,
        [Value] [nvarchar](500) NOT NULL,
        [Description] [nvarchar](200) NULL,
        [DataType] [nvarchar](20) NOT NULL,
        [CreatedUTC] [datetime] NOT NULL,
        [UpdatedUTC] [datetime] NULL,
    PRIMARY KEY CLUSTERED 
    (
        [ConfigId] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY],
    UNIQUE NONCLUSTERED 
    (
        [GroupId] ASC,
        [Key] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
    
    -- Add default constraint for CreatedUTC
    ALTER TABLE [dbo].[Config] ADD CONSTRAINT [DF_Config_CreatedUTC] DEFAULT (getutcdate()) FOR [CreatedUTC]
 
    -- Add default constraint for DataType
    ALTER TABLE [dbo].[Config] ADD CONSTRAINT [DF_Config_DataType] DEFAULT ('string') FOR [DataType]
    
    PRINT 'Config table created successfully.'
END
ELSE
BEGIN
    PRINT 'Config table already exists.'
END
GO

-- Insert configuration values from appsettings.json
-- General configurations (GroupId: 100)
INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
VALUES
    (100, 'SessionTimeoutMinutes', '30', 'Session timeout in minutes', 'int');

-- Background Processor configurations (GroupId: 200)
INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
VALUES
    (200, 'LessonCleanupEnabled', 'true', 'Enable/disable lesson cleanup service', 'bool'),
    (200, 'LessonCleanupIntervalHours', '24', 'Lesson cleanup interval in hours', 'int'),
    (200, 'LessonCleanupRetentionDays', '90', 'Lesson cleanup retention period in days', 'int'),
    (200, 'TutorCleanupEnabled', 'true', 'Enable/disable tutor cleanup service', 'bool'),
    (200, 'TutorCleanupIntervalHours', '24', 'Tutor cleanup interval in hours', 'int'),
    (200, 'TutorCleanupRetentionDays', '30', 'Tutor cleanup retention period in days', 'int'),
    (200, 'StudentCleanupEnabled', 'true', 'Enable/disable student cleanup service', 'bool'),
    (200, 'StudentCleanupIntervalHours', '24', 'Student cleanup interval in hours', 'int'),
    (200, 'StudentCleanupRetentionDays', '30', 'Student cleanup retention period in days', 'int'),
    (200, 'PaymentReminderEnabled', 'true', 'Enable/disable payment reminder service', 'bool'),
    (200, 'PaymentReminderIntervalHours', '24', 'Payment reminder interval in hours', 'int'),
    (200, 'PaymentReminderLessonThreshold', '3', 'Lesson threshold for payment reminders', 'int'),
    (200, 'PaymentReminderDeadlineDays', '7', 'Payment deadline in days', 'int');

-- UI configurations (GroupId: 300)
INSERT INTO [dbo].[Config] ([GroupId], [Key], [Value], [Description], [DataType])
VALUES
    (300, 'ShowActionButtonLabel', 'false', 'Show labels on action buttons', 'bool');

PRINT 'Configuration data inserted successfully.'
GO
